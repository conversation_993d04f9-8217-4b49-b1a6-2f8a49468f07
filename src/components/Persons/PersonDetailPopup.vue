<template>
  <div class="person-detail-popup-overlay">
    <div class="person-detail-container">
      <!-- 头部区域 -->
      <div class="person-header">
        <!-- 头像、按钮、关闭按钮包装区域 -->
        <div class="header-controls-wrapper">
          <!-- 左侧头像和姓名区域 -->
          <div class="avatar-name-section">
            <img
              :src="getAvatarUrl(personDetail?.avatar, personDetail?.person_id)"
              alt="头像"
              class="person-avatar clickable-avatar"
              @click="handleAvatarClick"
            />
            <!-- 人物名称显示在头像右侧 -->
            <div class="person-name">{{ personDetail?.canonical_name || personName }}</div>
          </div>

          <!-- 中间按钮区域 -->
          <div class="header-action-buttons"></div>

          <!-- 右上角关闭按钮 -->
          <div class="close-section">
            <button class="close-btn" @click="$emit('close')">
              <img src="@/assets/icon/close.png" alt="关闭" class="close-icon" />
            </button>
          </div>
        </div>
      </div>

      <!-- 内容区域 -->
      <div class="content-sections">
        <!-- 个人信息内容 - 默认显示 -->
        <div class="info-tab-content">
          <!-- 记忆时刻组件 -->
          <MemorySection
            ref="memorySectionRef"
            :person-detail="personDetail"
            :person-id="personId"
            :user-id="userId"
            @memory-add="handleMemoryAdd"
            @edit-memory="handleEditMemory"
            @delete-memory="handleDeleteMemory"
          />

          <!-- 个人信息组件 -->
          <InfoSection
            :person-detail="personDetail"
            :person-id="personId"
            :user-id="userId"
            @basic-info-mic-click="handleBasicInfoMicClick"
            @person-updated="handlePersonUpdated"
          />

          <!-- 提醒事项组件 - 只在核心节点显示 -->
          <ReminderSection
            v-if="isUserProfile"
            ref="reminderSectionRef"
            :person-detail="personDetail"
            :person-id="personId"
            :user-id="userId"
            :is-user-profile="isUserProfile"
            @add-reminder="handleAddReminder"
            @edit-reminder="handleEditReminder"
          />

          <!-- 天气分析组件 -->
          <WeatherSection
            :person-detail="personDetail"
            :person-id="personId"
            :user-id="userId"
            @weather-mic-click="handleWeatherMicClick"
            @person-updated="handlePersonUpdated"
          />
          <!-- 推荐话题组件 -->
          <TopicSection :person-id="personId" :user-id="userId" @topic-click="handleTopicClick" />

          <!-- 生活方式组件（去过&想去、饮食偏好、期望、其他属性） -->
          <LifestyleSection
            :person-detail="personDetail"
            :person-id="personId"
            :user-id="userId"
            @attributes-updated="handleAttributesUpdated"
            @show-voice-chat="handleShowVoiceChat"
          />

          <!-- 底部操作区域 - 移到内容区域最底部 -->
          <div v-if="!isUserProfile" class="person-footer-in-content">
            <p class="delete-hint-text">点此<span class="delete-action" @click="handleDeletePerson">删除</span>人员</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 删除人员确认对话框 -->
    <DeleteConfirmDialog
      :visible="showDeleteDialog"
      :content="`确定要删除 ${personDetail?.canonical_name} 吗？`"
      hint="删除后将无法恢复相关的记忆数据"
      :is-loading="isDeleting"
      @confirm="confirmDeletePerson"
      @cancel="closeDeleteDialog"
    />

    <!-- 编辑人员弹窗 -->
    <PersonEditDialog
      v-if="showEditDialog"
      :person="editPersonData"
      :user-id="userId"
      :is-user-profile="isUserProfile"
      @close="handleCloseEditDialog"
      @success="handleEditSuccess"
    />

    <!-- 头像选择弹窗 -->
    <AvatarSelectionDialog
      v-if="showAvatarSelectionDialog"
      @close="showAvatarSelectionDialog = false"
      @select-avatar="handleAvatarSelect"
      @upload-avatar="handleUploadAvatar"
    />

    <!-- 隐藏的头像上传组件 -->
    <div style="display: none">
      <AvatarUpload
        ref="avatarUploadRef"
        v-model="hiddenAvatarValue"
        :size="50"
        placeholder="上传头像"
        :max-size="10"
        @upload-success="handleAvatarUploadSuccess"
        @upload-error="handleAvatarUploadError"
      />
    </div>

    <!-- 添加提醒弹窗 -->
    <AddReminderDialog
      :show="showAddReminderDialog"
      :user-id="userId"
      :person-id="personId"
      @close="showAddReminderDialog = false"
      @success="handleAddReminderSuccess"
    />

    <!-- 编辑提醒弹窗 -->
    <AddReminderDialog
      :show="showEditReminderDialog"
      :user-id="userId"
      :person-id="personId"
      :edit-reminder="reminderToEdit"
      @close="showEditReminderDialog = false"
      @success="handleEditReminderSuccess"
    />

    <!-- 自然语言编辑提醒弹窗 -->
    <EditReminderDialog
      :show="showNaturalEditReminderDialog"
      :user-id="userId"
      :reminder-data="reminderToNaturalEdit"
      @close="showNaturalEditReminderDialog = false"
      @success="handleNaturalEditReminderSuccess"
    />

    <!-- 事件添加弹窗 -->
    <AddEventDialog
      :show="showEventAddPopup"
      :user-id="userId"
      :person-id="personId"
      @close="showEventAddPopup = false"
      @success="handleAddEventSuccess"
    />

    <!-- 删除记忆确认弹窗 -->
    <DeleteConfirmDialog
      :visible="showDeleteMemoryDialog"
      content="确定要删除这个事件记录吗？"
      :hint="memoryToDelete?.description_text || '事件记录'"
      @confirm="confirmDeleteMemory"
      @cancel="closeDeleteMemoryDialog"
    />

    <!-- 语音聊天弹窗 -->
    <VoiceChatDialog
      v-if="showVoiceChatDialog"
      :section-info="voiceChatSectionInfo"
      :person-name="personName"
      :user-id="userId"
      @close="handleVoiceChatDialogClose"
      @chat-complete="handleVoiceChatComplete"
    />

    <!-- 事件编辑弹窗 -->
    <EditEventDialog
      :show="showEventEditPopup"
      :user-id="userId"
      :event-data="editingEvent"
      @close="handleEventEditClose"
      @success="handleEventEditSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount, nextTick, computed, watch } from 'vue';
import {
  getPersonDetail,
  getPersonWeather,
  getPersonMemories,
  deletePersonEvent,
  type IPersonDetail,
  type IGetPersonWeatherResponse,
  type IGetPersonDetailResponse,
  type IReminder,
  type IRecommendedTopic,
  type IEvent,
} from '@/apis/memory';
import {
  type IPersonData,
  deletePerson,
  getUserProfile,
  updatePerson,
  type IGetUserProfileResponse,
} from '@/apis/relation';
import { showFailToast, showSuccessToast, showToast } from 'vant';
import {
  refreshWeatherAfterPersonUpdate,
  weatherRefreshManager,
  type IWeatherRefreshEvent,
} from '@/utils/weatherRefresh';
import { getAvatarUrl } from '@/utils/avatarUtils';
import { debounce } from 'lodash-es';
import AvatarSelectionDialog from '@/components/Common/AvatarSelectionDialog.vue';
import AvatarUpload from '@/components/Common/AvatarUpload.vue';
import AddEventDialog from '@/components/Dialogs/AddEventDialog.vue';
import EditEventDialog from '@/components/Dialogs/EditEventDialog.vue';
import DeleteConfirmDialog from '@/components/Common/DeleteConfirmDialog.vue';
import AddReminderDialog from '@/components/Dialogs/AddReminderDialog.vue';
import EditReminderDialog from '@/components/Dialogs/EditReminderDialog.vue';
import VoiceChatDialog from '@/components/Dialogs/VoiceChatDialog.vue';
import MemorySection from '@/components/Sections/MemorySection.vue';
import ReminderSection from '@/components/Sections/ReminderSection.vue';
import InfoSection from '@/components/Sections/InfoSection.vue';
import WeatherSection from '@/components/Sections/WeatherSection.vue';
import LifestyleSection from '@/components/Sections/LifestyleSection.vue';
import TopicSection from '@/components/Sections/TopicSection.vue';
import PersonEditDialog from './PersonEditDialog.vue';

// Props定义
interface IProps {
  userId: string;
  personId: string;
  personName: string;
  isUserProfile?: boolean; // 新增：标识是否为用户档案
}

const props = defineProps<IProps>();

// Emits定义
const emit = defineEmits<{
  close: [];
  refresh: [];
}>();

// 响应式数据
const loading = ref(true);
const loadingWeather = ref(true);
const personDetail = ref<IPersonDetail | null>(null);
const weatherData = ref<IGetPersonWeatherResponse | null>(null);

// 提醒相关
const showAddReminderDialog = ref(false);
const showEditReminderDialog = ref(false);
const showNaturalEditReminderDialog = ref(false);

const reminderToNaturalEdit = ref<IReminder | null>(null);

// 删除记忆确认弹窗相关
const showDeleteMemoryDialog = ref(false);
const memoryToDelete = ref<IEvent | null>(null);

const reminderToEdit = ref<IReminder | null>(null);
const isReminderExpanded = ref(false); // ReminderSection相关

// 记忆时刻相关 - MemorySection相关，可在未来进一步重构时优化
const isMemoryExpanded = ref(false);
const showEventAddPopup = ref(false);
const showEventEditPopup = ref(false);
const editingEvent = ref<IEvent | null>(null);
const memorySectionRef = ref<InstanceType<typeof MemorySection> | null>(null);
const reminderSectionRef = ref<InstanceType<typeof ReminderSection> | null>(null);

// 个人信息展开状态 - InfoWeatherSection相关，可在未来进一步重构时优化
const isInfoExpanded = ref(false);

// 天气分析展开状态 - InfoWeatherSection相关，可在未来进一步重构时优化
const isWeatherExpanded = ref(false);

// 编辑状态相关 - 已抽离到各自组件中，这些变量可以删除

// LifestyleSection相关状态变量已移至组件内部

// 计算属性：获取处理后的key_attributes对象
const processedKeyAttributes = computed(() => {
  if (!personDetail.value || !personDetail.value.key_attributes) {
    return {};
  }

  let attributes = personDetail.value.key_attributes;

  // 如果是字符串，尝试解析为对象
  if (typeof attributes === 'string') {
    try {
      attributes = JSON.parse(attributes);
      console.log('✅ [PersonDetailPopup] key_attributes字符串解析成功:', attributes);
    } catch (error) {
      console.warn('⚠️ [PersonDetailPopup] key_attributes字符串解析失败:', error, '原始数据:', attributes);
      return {};
    }
  }

  // 确保是对象类型
  if (typeof attributes !== 'object' || attributes === null) {
    console.warn('⚠️ [PersonDetailPopup] key_attributes不是有效对象:', attributes);
    return {};
  }

  // 过滤掉空值的属性
  const filteredAttributes: Record<string, string> = {};
  Object.entries(attributes).forEach(([key, value]) => {
    if (value && String(value).trim()) {
      filteredAttributes[key] = String(value);
    }
  });

  return filteredAttributes;
});

// 计算属性：获取当前城市的天气数据
const currentCityWeatherData = computed(() => {
  if (!weatherData.value || weatherData.value.result !== 'success' || !weatherData.value.weather_data) {
    return null;
  }

  // 查找当前城市的天气数据
  const attributes = processedKeyAttributes.value;
  const currentCity = attributes['当前城市'];

  if (!currentCity) {
    return null;
  }

  // 在weather_data中查找匹配的城市数据
  const weatherEntries = Object.entries(weatherData.value.weather_data);
  const matchingEntry = weatherEntries.find(
    ([locationKey, cityData]) => locationKey === '当前城市' || cityData.city === currentCity,
  );

  return matchingEntry ? matchingEntry[1] : null;
});

// 获取旅游历史属性值
const getTravelHistory = (): string => {
  const attributes = processedKeyAttributes.value;
  return attributes['旅游历史'] || '';
};

// 获取旅游历史列表（分割后的数组）
const getTravelHistoryList = (): string[] => {
  const travelHistory = getTravelHistory();
  if (!travelHistory) return [];
  return travelHistory
    .split('|')
    .map((item) => item.trim())
    .filter((item) => item.length > 0);
};

// 获取餐饮偏好属性值
const getFoodPreference = (): string => {
  const attributes = processedKeyAttributes.value;
  return attributes['餐饮偏好'] || '';
};

// 获取餐饮偏好列表（分割后的数组）
const getFoodPreferenceList = (): string[] => {
  const foodPreference = getFoodPreference();
  if (!foodPreference) return [];
  return foodPreference
    .split('|')
    .map((item) => item.trim())
    .filter((item) => item.length > 0);
};

// 获取期望属性值
const getExpectation = (): string => {
  const attributes = processedKeyAttributes.value;
  return attributes['期望'] || '';
};

// 获取期望列表（分割后的数组）
const getExpectationList = (): string[] => {
  const expectation = getExpectation();
  if (!expectation) return [];
  return expectation
    .split('|')
    .map((item) => item.trim())
    .filter((item) => item.length > 0);
};

// 获取其他属性列表（除了已经单独显示的属性）
const getOtherAttributes = (): Array<{ key: string; value: string }> => {
  const attributes = processedKeyAttributes.value;
  const excludedKeys = ['旅游历史', '餐饮偏好', '期望', '当前城市']; // 排除已经单独显示的属性

  return Object.entries(attributes)
    .filter(([key]) => !excludedKeys.includes(key))
    .map(([key, value]) => ({ key, value }));
};

// 编辑弹窗相关
const showEditDialog = ref(false);
const editPersonData = ref<IPersonData | null>(null);

// 头像选择弹窗相关
const showAvatarSelectionDialog = ref(false);
const avatarUploadRef = ref();
const hiddenAvatarValue = ref('');

// 删除确认弹窗相关
const showDeleteDialog = ref(false);
const isDeleting = ref(false);

// 判断是否为用户档案
const isUserProfile = computed(() => props.isUserProfile || false);

// 将IPersonDetail转换为IPersonData的函数
const convertPersonDetailToPersonData = (detail: IPersonDetail): IPersonData => {
  // 处理key_attributes，确保它是对象格式并过滤空值
  let keyAttributes: Record<string, string> = {};
  if (detail.key_attributes) {
    if (typeof detail.key_attributes === 'string') {
      try {
        const parsed = JSON.parse(detail.key_attributes) as Record<string, unknown>;
        // 过滤掉空值
        Object.entries(parsed).forEach(([key, value]) => {
          if (value && String(value).trim()) {
            keyAttributes[key] = String(value);
          }
        });
      } catch (error) {
        console.warn('解析key_attributes失败:', error);
        keyAttributes = {};
      }
    } else {
      // 过滤掉空值
      Object.entries(detail.key_attributes).forEach(([key, value]) => {
        if (value && String(value).trim()) {
          keyAttributes[key] = String(value);
        }
      });
    }
  }

  // 处理relationships，确保它是数组格式
  let relationships: string[] = [];
  if (detail.relationships) {
    if (typeof detail.relationships === 'string') {
      try {
        relationships = JSON.parse(detail.relationships);
      } catch (error) {
        console.warn('解析relationships失败:', error);
        relationships = [];
      }
    } else {
      relationships = detail.relationships;
    }
  }

  return {
    person_id: detail.person_id,
    user_id: detail.user_id,
    canonical_name: detail.canonical_name,
    aliases: detail.aliases,
    relationships,
    profile_summary: detail.profile_summary,
    key_attributes: keyAttributes,
    avatar: detail.avatar,
    is_user: detail.is_user,
  };
};

// 处理编辑弹窗关闭
const handleCloseEditDialog = () => {
  showEditDialog.value = false;
  editPersonData.value = null;
};

// 处理编辑成功
const handleEditSuccess = () => {
  console.log('编辑人员信息成功');
  // 重新加载人员详情数据
  void loadPersonDetail();
  // 通知父组件刷新关系图
  emit('refresh');
};

// 处理头像点击
const handleAvatarClick = () => {
  console.log('头像点击，显示头像选择弹窗');
  showAvatarSelectionDialog.value = true;
};

// 处理头像选择
const handleAvatarSelect = async (selectedAvatarId: string) => {
  console.log('✅ [PersonDetailPopup] 选择头像:', selectedAvatarId);

  if (!personDetail.value) return;

  try {
    // 转换数据格式并更新头像
    const personData = convertPersonDetailToPersonData(personDetail.value);

    // 处理aliases字段，确保格式正确
    let submitAliases = personData.aliases || '';
    if (typeof submitAliases !== 'string') {
      submitAliases = '';
    }
    if (submitAliases === '') {
      submitAliases = '[]';
    } else if (!submitAliases.startsWith('[')) {
      // 如果不是JSON格式，按、分割并转换为JSON格式
      const aliasArray = submitAliases
        .split('、')
        .map((alias) => alias.trim())
        .filter((alias) => alias.length > 0)
        .map((alias) => `"${alias}"`);

      if (aliasArray.length > 0) {
        submitAliases = `[${aliasArray.join(',')}]`;
      } else {
        submitAliases = '[]';
      }
    }

    const response = await updatePerson(personDetail.value.person_id, {
      user_id: props.userId,
      canonical_name: personData.canonical_name,
      aliases: submitAliases,
      relationships: personData.relationships as string[],
      profile_summary: personData.profile_summary,
      key_attributes: personData.key_attributes as Record<string, unknown>,
      is_user: personData.is_user,
      avatar: selectedAvatarId,
    });

    if (response && response.result === 'success') {
      console.log('✅ [PersonDetailPopup] 头像更新成功');
      showSuccessToast('头像更新成功');

      // 重新加载人员详情数据
      void loadPersonDetail();
      // 通知父组件刷新关系图
      emit('refresh');

      // 刷新天气数据
      try {
        await refreshWeatherAfterPersonUpdate(props.userId, props.personId);
      } catch (error) {
        console.error('❌ [PersonDetailPopup] 刷新天气数据失败:', error);
      }
    } else {
      console.warn('⚠️ [PersonDetailPopup] 头像更新失败:', response);
      showFailToast('头像更新失败');
    }
  } catch (error) {
    console.error('❌ [PersonDetailPopup] 头像更新失败:', error);
    showFailToast('头像更新失败');
  } finally {
    showAvatarSelectionDialog.value = false;
  }
};

// 处理上传头像
const handleUploadAvatar = () => {
  showAvatarSelectionDialog.value = false;
  // 触发隐藏的AvatarUpload组件的上传功能
  if (avatarUploadRef.value && avatarUploadRef.value.triggerUpload) {
    avatarUploadRef.value.triggerUpload();
  }
};

// 处理头像上传成功
const handleAvatarUploadSuccess = async (url: string) => {
  console.log('✅ [PersonDetailPopup] 头像上传成功:', url);
  await handleAvatarSelect(url);
};

// 处理头像上传失败
const handleAvatarUploadError = (error: string) => {
  console.error('❌ [PersonDetailPopup] 头像上传失败:', error);
};

// 处理删除人员按钮点击
const handleDeletePerson = () => {
  showDeleteDialog.value = true;
};

// 关闭删除确认对话框
const closeDeleteDialog = () => {
  showDeleteDialog.value = false;
};

// 确认删除人员
const confirmDeletePerson = async () => {
  if (!personDetail.value) return;

  try {
    isDeleting.value = true;
    console.log('🔄 [PersonDetailPopup.vue] 开始删除人员...', {
      userId: props.userId,
      personId: props.personId,
      name: personDetail.value.canonical_name,
    });

    const response = await deletePerson(props.userId, props.personId);

    console.log('📡 [PersonDetailPopup.vue] 删除人员响应:', response);

    if (response && response.result === 'success') {
      console.log('✅ [PersonDetailPopup.vue] 人员删除成功');
      showSuccessToast('删除成功');

      // 关闭删除对话框
      closeDeleteDialog();

      // 通知父组件刷新关系图
      emit('refresh');

      // 关闭人员详情弹窗
      emit('close');
    } else {
      console.warn('⚠️ [PersonDetailPopup.vue] 删除人员失败:', response);
      showFailToast('删除人员失败');
    }
  } catch (error) {
    console.error('❌ [PersonDetailPopup.vue] 删除人员失败:', error);
    showFailToast('删除人员失败');
  } finally {
    isDeleting.value = false;
  }
};

// 处理话题点击 (TopicSection相关，可在未来进一步重构时优化)
const handleTopicClick = (topic: IRecommendedTopic) => {
  console.log('💬 [PersonDetailPopup] 话题点击:', topic);
  // 这里可以添加话题点击的处理逻辑，比如跳转到聊天页面
};

// 处理人员信息更新
const handlePersonUpdated = (updatedPersonDetail: IPersonDetail) => {
  personDetail.value = updatedPersonDetail;
  console.log('✅ [PersonDetailPopup] 人员信息已更新:', updatedPersonDetail);
};

// 处理记忆时刻添加按钮点击
const handleMemoryAdd = () => {
  console.log('➕ [PersonDetailPopup] 事件记录添加按钮点击');
  showEventAddPopup.value = true;
};

// 处理添加事件成功
const handleAddEventSuccess = () => {
  console.log('✅ [PersonDetailPopup] 事件添加成功');
  showEventAddPopup.value = false;
  // 延迟50ms后刷新MemorySection数据，确保后端数据已更新
  setTimeout(() => {
    if (memorySectionRef.value) {
      memorySectionRef.value.loadMemories();
    }
  }, 50);
};

// 语音聊天弹窗相关状态
const showVoiceChatDialog = ref(false);
const voiceChatSectionInfo = ref<{
  title: string;
  icon: string;
  content: string;
}>({
  title: '',
  icon: '',
  content: '',
});

// 处理各个section的mic按钮点击事件
const handleBasicInfoMicClick = () => {
  console.log('个人信息mic按钮点击');
  voiceChatSectionInfo.value = {
    title: '个人信息',
    icon: '📋',
    content: personDetail.value?.profile_summary || '暂无个人信息',
  };
  showVoiceChatDialog.value = true;
};

const handleWeatherMicClick = () => {
  console.log('天气分析mic按钮点击');
  const attributes = processedKeyAttributes.value;
  voiceChatSectionInfo.value = {
    title: '天气分析',
    icon: '☀️',
    content: attributes['当前城市'] || '暂无天气信息',
  };
  showVoiceChatDialog.value = true;
};

// 处理属性更新
const handleAttributesUpdated = (newAttributes: Record<string, string>) => {
  if (!personDetail.value) return;

  // 更新本地数据
  const updatedPersonDetail = {
    ...personDetail.value,
    key_attributes: newAttributes,
  };
  personDetail.value = updatedPersonDetail;
};

// 处理语音聊天显示
const handleShowVoiceChat = (sectionInfo: { title: string; icon: string; content: string }) => {
  voiceChatSectionInfo.value = {
    title: sectionInfo.title,
    icon: sectionInfo.icon,
    content: sectionInfo.content,
  };
  showVoiceChatDialog.value = true;
};

// 处理语音聊天弹窗关闭
const handleVoiceChatDialogClose = () => {
  showVoiceChatDialog.value = false;
};

// 更新VoiceChatDialog中显示的section信息
const updateVoiceChatSectionInfo = () => {
  const currentTitle = voiceChatSectionInfo.value.title;

  if (currentTitle === '个人信息') {
    voiceChatSectionInfo.value = {
      title: '个人信息',
      icon: '📋',
      content: personDetail.value?.profile_summary || '暂无个人信息',
    };
  } else if (currentTitle === '天气分析') {
    const attributes = processedKeyAttributes.value;
    voiceChatSectionInfo.value = {
      title: '天气分析',
      icon: '☀️',
      content: attributes['当前城市'] || '暂无天气信息',
    };
  } else if (currentTitle === '去过&想去') {
    const attributes = processedKeyAttributes.value;
    voiceChatSectionInfo.value = {
      title: '去过&想去',
      icon: '✈️',
      content: attributes['旅游历史'] || '暂无旅行记录',
    };
  } else if (currentTitle === '饮食偏好') {
    const attributes = processedKeyAttributes.value;
    voiceChatSectionInfo.value = {
      title: '饮食偏好',
      icon: '🍽️',
      content: attributes['餐饮偏好'] || '暂无饮食偏好信息',
    };
  } else if (currentTitle === '我的期望') {
    const attributes = processedKeyAttributes.value;
    voiceChatSectionInfo.value = {
      title: '我的期望',
      icon: '🌟',
      content: attributes['期望'] || '暂无期望信息',
    };
  }
};

// 处理语音聊天完成事件
const handleVoiceChatComplete = async () => {
  // 调用数据刷新函数
  await refreshDataAfterChat();

  // 数据刷新完成后，更新VoiceChatDialog中显示的section信息
  updateVoiceChatSectionInfo();
};

// 对话结束后刷新数据
const refreshDataAfterChat = async () => {
  console.log('🔄 [PersonDetailPopup] 对话结束，开始刷新人员信息和提醒数据...');

  try {
    // 并行调用API刷新数据
    await Promise.all([refreshPersonDetail(), refreshReminders()]);
    // 显示成功提示
    showSuccessToast('信息已更新');
  } catch (error) {
    console.error('❌ [PersonDetailPopup] 刷新数据失败:', error);
    showFailToast('刷新数据失败');
  }
};

// 刷新人员详情数据
const refreshPersonDetail = async () => {
  await loadPersonDetail();
};

// 刷新提醒数据
const refreshReminders = async () => {
  if (reminderSectionRef.value) {
    await reminderSectionRef.value.loadReminders();
  }
};

// 获取人员详情数据
const loadPersonDetail = async () => {
  try {
    loading.value = true;
    console.log('🔄 [PersonDetailPopup.vue] 开始获取人员详情...', {
      userId: props.userId,
      personId: props.personId,
      personName: props.personName,
      isUserProfile: isUserProfile.value,
    });

    if (!props.userId || !props.personId) {
      console.error('❌ [PersonDetailPopup.vue] 缺少必要参数');
      return;
    }

    let response: IGetUserProfileResponse | IGetPersonDetailResponse;

    // 判断是否为用户档案
    if (isUserProfile.value) {
      // 获取用户档案
      response = await getUserProfile({
        user_id: props.userId,
      });
    } else {
      // 获取普通人员详情
      response = await getPersonDetail({
        user_id: props.userId,
        person_id: props.personId,
      });
    }

    console.log('📡 [PersonDetailPopup.vue] 人员详情响应:', response);

    if (response && response.result === 'success' && response.person) {
      // 类型转换：将IPersonData转换为IPersonDetail兼容格式
      personDetail.value = response.person as IPersonDetail;
    } else if (isUserProfile.value && 'reason' in response) {
      showFailToast(typeof response.reason === 'string' ? response.reason : '获取用户档案失败');
    }
  } catch (error) {
    showFailToast('获取人员详情失败');
  } finally {
    loading.value = false;
  }
};

// 获取天气数据
const loadWeatherData = async () => {
  try {
    loadingWeather.value = true;

    if (!props.userId || !props.personId) {
      console.error('❌ [PersonDetailPopup.vue] 缺少必要参数');
      weatherData.value = null;
      return;
    }

    console.log('🔄 [PersonDetailPopup.vue] 开始获取天气数据...', {
      userId: props.userId,
      personId: props.personId,
      isUserProfile: isUserProfile.value,
    });

    const response = await getPersonWeather({
      user_id: props.userId,
      person_id: props.personId,
    });

    console.log('📡 [PersonDetailPopup.vue] 天气数据响应:', response);

    if (response && response.result === 'success') {
      weatherData.value = response;
      console.log('✅ [PersonDetailPopup.vue] 天气数据加载成功');
    } else if (response && response.result === 'error') {
      weatherData.value = response;
      console.log('⚠️ [PersonDetailPopup.vue] 天气数据返回错误:', response.reason);
    } else {
      console.warn('⚠️ [PersonDetailPopup.vue] 天气数据格式异常:', response);
      weatherData.value = null;
    }
  } catch (error) {
    console.error('❌ [PersonDetailPopup.vue] 获取天气数据失败:', error);
    // 区分不同类型的错误
    if (error instanceof Error) {
      if (error.message.includes('timeout') || error.message.includes('Fetch timeout')) {
        console.error('❌ [PersonDetailPopup.vue] 天气API请求超时');
      } else if (error.message.includes('network') || error.message.includes('Failed to fetch')) {
        console.error('❌ [PersonDetailPopup.vue] 天气API网络错误');
      } else {
        console.error('❌ [PersonDetailPopup.vue] 天气API其他错误:', error.message);
      }
    }
    weatherData.value = null;
  } finally {
    loadingWeather.value = false;
  }
};

// 处理添加提醒
const handleAddReminder = () => {
  reminderToEdit.value = null; // 清空编辑状态
  showAddReminderDialog.value = true;
};

// 切换提醒事项展开/收起状态
const toggleReminderExpanded = () => {
  isReminderExpanded.value = !isReminderExpanded.value;
};

// 切换个人信息展开/收起状态
const toggleInfoExpanded = () => {
  isInfoExpanded.value = !isInfoExpanded.value;
};

// 处理个人信息内容点击事件 - 区分点击展开和复制操作
const handleInfoContentClick = (event: Event) => {
  // 编辑功能已移至InfoWeatherSection组件内部

  // 检查是否是文本选择操作（复制操作）
  const selection = window.getSelection();
  if (selection && selection.toString().length > 0) {
    // 有文本被选中，这是复制操作，不触发展开/收起
    return;
  }

  // 否则触发展开/收起
  toggleInfoExpanded();
};

// 切换天气分析展开/收起状态
const toggleWeatherExpanded = () => {
  isWeatherExpanded.value = !isWeatherExpanded.value;
};

// 处理天气分析内容点击事件 - 区分点击展开和复制操作
const handleWeatherContentClick = (event: Event) => {
  // 编辑功能已移至InfoWeatherSection组件内部

  // 检查是否是文本选择操作（复制操作）
  const selection = window.getSelection();
  if (selection && selection.toString().length > 0) {
    // 有文本被选中，这是复制操作，不触发展开/收起
    return;
  }

  // 否则触发展开/收起
  toggleWeatherExpanded();
};

// 处理编辑提醒 - 使用自然语言编辑
const handleEditReminder = (reminder: IReminder) => {
  reminderToNaturalEdit.value = reminder;
  showNaturalEditReminderDialog.value = true;
};

// 处理添加提醒成功
const handleAddReminderSuccess = () => {
  showAddReminderDialog.value = false;
  // 刷新ReminderSection数据
  if (reminderSectionRef.value) {
    reminderSectionRef.value.loadReminders();
  }
};

// 处理编辑提醒成功
const handleEditReminderSuccess = () => {
  showEditReminderDialog.value = false;
  reminderToEdit.value = null;
  // 刷新ReminderSection数据
  if (reminderSectionRef.value) {
    reminderSectionRef.value.loadReminders();
  }
};

// 处理自然语言编辑提醒成功
const handleNaturalEditReminderSuccess = () => {
  showNaturalEditReminderDialog.value = false;
  reminderToNaturalEdit.value = null;
  // 刷新ReminderSection数据
  if (reminderSectionRef.value) {
    reminderSectionRef.value.loadReminders();
  }
};

// 处理编辑记忆
const handleEditMemory = (memory: IEvent) => {
  editingEvent.value = memory;
  showEventEditPopup.value = true;
};

// 处理删除记忆
const handleDeleteMemory = (memory: IEvent) => {
  memoryToDelete.value = memory;
  showDeleteMemoryDialog.value = true;
};

// 确认删除记忆
const confirmDeleteMemory = async () => {
  if (!memoryToDelete.value) return;

  try {
    console.log('🔄 [PersonDetailPopup.vue] 开始删除记忆...', {
      userId: props.userId,
      eventId: memoryToDelete.value.event_id,
    });

    const response = await deletePersonEvent({
      user_id: props.userId,
      event_id: memoryToDelete.value.event_id,
    });

    console.log('📡 [PersonDetailPopup.vue] 删除记忆响应:', response);

    if (response && response.result === 'success') {
      console.log('✅ [PersonDetailPopup.vue] 记忆删除成功');
      showSuccessToast('记忆删除成功');

      // 记忆已从MemorySection组件中删除

      // 关闭删除对话框
      closeDeleteMemoryDialog();
    } else {
      console.warn('⚠️ [PersonDetailPopup.vue] 删除记忆失败:', response);
      showFailToast(response?.reason || '删除记忆失败');
    }
  } catch (error) {
    console.error('❌ [PersonDetailPopup.vue] 删除记忆失败:', error);
    showFailToast('删除记忆失败');
  }
};

// 关闭删除记忆对话框
const closeDeleteMemoryDialog = () => {
  showDeleteMemoryDialog.value = false;
  memoryToDelete.value = null;
};

// 处理事件编辑弹窗关闭
const handleEventEditClose = () => {
  showEventEditPopup.value = false;
  editingEvent.value = null;
};

// 处理事件编辑成功
const handleEventEditSuccess = () => {
  showEventEditPopup.value = false;
  editingEvent.value = null;
  // 延迟50ms后刷新MemorySection数据，确保后端数据已更新
  setTimeout(() => {
    if (memorySectionRef.value) {
      memorySectionRef.value.loadMemories();
    }
  }, 50);
};

// 天气数据刷新订阅
let weatherRefreshUnsubscribe: (() => void) | null = null;

// 处理天气数据刷新事件
const handleWeatherRefresh = (event: IWeatherRefreshEvent) => {
  if (event.type === 'person-weather' && event.personId === props.personId) {
    console.log('🔄 [PersonDetailPopup] 收到人员天气数据刷新事件，更新weather-reminder');
    // 更新天气数据
    weatherData.value = event.data as IGetPersonWeatherResponse;
  }
};

// 组件挂载时加载数据
onMounted(() => {
  void loadPersonDetail();
  void loadWeatherData();

  // 订阅天气数据刷新事件
  weatherRefreshUnsubscribe = weatherRefreshManager.subscribe(handleWeatherRefresh);
});

// 组件卸载前清理资源
onBeforeUnmount(() => {
  console.log('🧹 [PersonDetailPopup] 组件卸载，清理资源');

  // 取消天气数据刷新订阅
  if (weatherRefreshUnsubscribe) {
    weatherRefreshUnsubscribe();
    weatherRefreshUnsubscribe = null;
  }
});
</script>

<style lang="scss" scoped>
.person-detail-popup-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(20px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  animation: fadeIn 0.3s ease-out;
}

.person-detail-container {
  background: rgba(1, 28, 32, 0.6);
  border: none;
  border-radius: 16px;
  padding: 30px;
  padding-bottom: 0; // 底部不需要padding，为chat-input-container留空间
  box-sizing: border-box;
  backdrop-filter: blur(10px);
  border-left: 4px solid #00ffff;
  box-shadow: -4px 0 8px rgba(0, 255, 255, 0.3);
  transition: all 0.3s ease;
  width: 600px;
  height: 1200px; // 固定高度
  color: white;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  display: flex;
  flex-direction: column;
  overflow: hidden; // 防止整个容器滚动
  animation: slideInScale 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.person-header {
  display: flex;
  flex-direction: column;
  position: relative;
}

.header-controls-wrapper {
  display: flex;
  justify-content: space-between;
  align-items: center; // 改为居中对齐，保持一致高度
  position: relative;
  height: 72px; // 调整头部高度以适应新的布局
}

.avatar-name-section {
  display: flex;
  align-items: center;
  height: 60px; // 调整高度以适应缩小的头像
  gap: 16px; // 头像和姓名之间的间距

  .person-avatar {
    width: 60px; // 缩小头像尺寸
    height: 60px;
    border-radius: 50%;
    object-fit: cover;
    border: 3px solid rgba(139, 69, 19, 0.8); // 棕色边框
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);

    &.clickable-avatar {
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        border-color: #00ffff;
        box-shadow: 0 0 15px rgba(0, 255, 255, 0.5);
        transform: scale(1.05);
      }
    }
  }

  .person-name {
    font-size: 28px; // 增加4px (原来24px)
    font-weight: 600;
    color: rgba(255, 255, 255, 0.9);
    text-align: left;
  }
}

.header-action-buttons {
  display: flex;
  gap: 20px;
  align-items: center;
  flex: 1;
  justify-content: center; // 改为居中对齐
  height: 72px;
}

.close-section {
  display: flex;
  align-items: center;
  height: 72px; // 调整高度以适应新的布局

  .close-btn {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    width: 48px;
    height: 48px;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      background: rgba(255, 255, 255, 0.2);
      border-color: rgba(255, 255, 255, 0.4);
      transform: scale(1.1);
    }

    .close-icon {
      width: 24px;
      height: 24px;
      filter: brightness(0) invert(1);
    }
  }
}

.content-sections {
  display: flex;
  flex-direction: column;
  flex: 1; // 占据剩余空间
  overflow-y: auto; // 允许内容区域滚动
  padding-right: 4px; // 为滚动条留出空间
  min-height: 0; // 确保flex子项可以收缩

  &::-webkit-scrollbar {
    width: 4px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 2px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 2px;

    &:hover {
      background: rgba(255, 255, 255, 0.5);
    }
  }
}

.attribute-section,
.topic-section,
.info-display-section {
  border: none;
  border-radius: 16px;
  padding: 22px; // 增加2px上下padding (原来20px)
  margin-top: 24px;
  background: rgba(0, 188, 212, 0.05);
  backdrop-filter: blur(10px);
  border-left: 4px solid #00ffff;
  box-shadow: -4px 0 8px rgba(0, 255, 255, 0.3);
  max-height: 260px;
  display: flex;
  flex-direction: column;
}
.weather-section,
.info-section {
  border: none;
  border-radius: 16px;
  padding: 22px; // 增加2px上下padding (原来20px)
  margin-top: 24px;
  background: rgba(0, 188, 212, 0.05);
  backdrop-filter: blur(10px);
  border-left: 4px solid #00ffff;
  box-shadow: -4px 0 8px rgba(0, 255, 255, 0.3);
  display: flex;
  flex-direction: column;
}

// 个人信息展开/收起控制
.info-section .section-content {
  cursor: pointer;
  transition: max-height 0.3s ease;

  &:not(.expanded) {
    max-height: 200px;
    overflow: hidden;
  }

  &.expanded {
    max-height: none;
    overflow: visible;
  }
}

// 天气分析展开/收起控制
.weather-section .section-content {
  cursor: pointer;
  transition: max-height 0.3s ease;

  &:not(.expanded) {
    max-height: 200px;
    overflow: hidden;
  }

  &.expanded {
    max-height: none;
    overflow: visible;
  }
}

// 信息显示区域样式
.info-display-section {
  .info-display-content {
    .info-item {
      display: flex;
      margin-bottom: 8px;
      font-size: 24px; // 增加8px (原来16px)

      .info-label {
        color: #00ffff;
        font-weight: 500;
        min-width: 80px;
        margin-right: 8px;
        font-size: 30px; // 增加8px (原来22px)
      }

      .info-value {
        color: white;
        flex: 1;
        word-break: break-word;
        font-size: 30px; // 增加8px (原来22px)
      }
    }

    .attributes-display {
      margin-top: 12px;
    }
  }
}

.section-header {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  gap: 8px;

  .section-icon {
    font-size: 32px; // 增加8px (原来24px)
  }

  .section-title {
    color: rgba(255, 255, 255, 0.9);
    font-size: 34px; // 增加8px (原来26px)
    font-weight: 600;
    flex: 1;
  }

  .section-actions {
    display: flex;
    align-items: center;
    gap: 14px; // 增加6px间距防止误触 (原来8px)
  }

  .section-edit-btn,
  .section-mic-btn {
    width: 42px; // 适当增大以适应页面文字变大 (原来36px)
    height: 42px;
    border-radius: 50%;
    border: 2px solid #00bcd4;
    background: transparent;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;

    &:hover {
      background: rgba(0, 188, 212, 0.1);
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 188, 212, 0.3);
    }

    .section-edit-icon,
    .section-mic-icon {
      width: 18px;
      height: 18px;
      filter: brightness(0) saturate(100%) invert(77%) sepia(93%) saturate(1352%) hue-rotate(169deg) brightness(97%)
        contrast(96%);
    }
  }
}

.section-content {
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.6;
  flex: 1;
  overflow-y: auto;
  min-height: 0;
}

.loading-text {
  color: rgba(255, 255, 255, 0.6);
  font-style: italic;
  text-align: center;
  padding: 10px 0;
  font-size: 32px; // 增加8px (原来24px)
}

.info-content {
  .info-text-container {
    .info-text {
      margin: 0 0 16px 0;
      font-size: 32px; // 增加8px (原来24px)
      line-height: 1.6;
      transition: all 0.3s ease;

      &.collapsed {
        display: -webkit-box;
        -webkit-line-clamp: 3;
        line-clamp: 3;
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }
}

// 新的底部操作区域样式 - 位于内容区域内部
.person-footer-in-content {
  margin-top: 40px; // 与上方内容保持距离
  padding: 30px 0; // 上下内边距
  border-top: 1px solid rgba(255, 255, 255, 0.1); // 更淡的分割线
  text-align: center;

  .delete-hint-text {
    font-size: 24px;
    color: rgba(255, 255, 255, 0.7);
    margin: 0;
    line-height: 1.5;

    .delete-action {
      color: #ef4444;
      cursor: pointer;
      font-weight: 600;
      transition: all 0.3s ease;
      text-decoration: underline;
      text-decoration-color: transparent;

      &:hover {
        color: #dc2626;
        text-decoration-color: #dc2626;
        text-shadow: 0 0 8px rgba(239, 68, 68, 0.5);
      }
    }
  }
}

.chat-input-container {
  padding: 20px 0px 30px 0px; // 移除左右内边距，让内容占满宽度
  background: transparent; // 改为透明背景
  border-radius: 0 0 16px 16px; // 只有底部圆角，与弹窗底部贴合
  border: none; // 移除边框
  box-shadow: none; // 移除阴影
  margin-top: auto; // 推到底部
  flex-shrink: 0; // 防止被压缩
}

.chat-input-wrapper {
  display: flex;
  gap: 16px; // 增大间距
  align-items: center;
  position: relative;
  width: 100%; // 占满容器宽度
  padding: 0 0px; // 在wrapper内部添加左右内边距
  box-sizing: border-box;

  .chat-input {
    flex: 1;
    padding: 18px 22px; // 进一步增大内边距
    border: 2px solid rgba(0, 188, 212, 0.3);
    border-radius: 30px; // 进一步增大圆角
    background: rgba(255, 255, 255, 0.1);
    color: white;
    font-size: 30px; // 增加8px (原来22px)
    outline: none;
    transition: all 0.3s ease;
    min-height: 56px; // 增加最小高度
    box-sizing: border-box;

    &::placeholder {
      color: rgba(255, 255, 255, 0.5);
    }

    &:focus {
      border-color: #00bcd4;
      background: rgba(255, 255, 255, 0.15);
      box-shadow: 0 0 0 3px rgba(0, 188, 212, 0.2);
    }

    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }
  }

  .send-btn {
    width: 56px; // 进一步增大按钮尺寸
    height: 56px;
    border-radius: 50%;
    border: 2px solid #00bcd4;
    background: #00bcd4;
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;

    &:hover:not(:disabled) {
      background: #00acc1;
      border-color: #00acc1;
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 188, 212, 0.4);
    }

    &:disabled {
      opacity: 0.5;
      cursor: not-allowed;
      transform: none;
      box-shadow: none;
    }

    svg {
      width: 26px; // 进一步增大图标尺寸
      height: 26px;
    }
  }
}
</style>
